import React, { useState } from 'react';
import styled from 'styled-components';
import { GitBranch, Plus, Trash2, GitMerge, MoreHorizontal, CheckCircle } from 'lucide-react';
import { useGit } from '../../contexts/GitContext';
import { useTheme } from '../../contexts/ThemeContext';
import { Branch } from '../../types/git';

const BranchListContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const BranchListHeader = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const BranchCount = styled.span<{ theme: any }>`
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
`;

const CreateBranchButton = styled.button<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.primaryHover};
  }
`;

const BranchListContent = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const BranchSection = styled.div<{ theme: any }>`
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const SectionHeader = styled.div<{ theme: any }>`
  padding: 0.75rem 1rem;
  background: ${props => props.theme.colors.surface};
  font-weight: 600;
  font-size: 0.9rem;
  color: ${props => props.theme.colors.text};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const BranchItem = styled.div<{ theme: any; current?: boolean }>`
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.current ? props.theme.colors.primary + '10' : 'transparent'};
  cursor: pointer;
  transition: background 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
  }

  &:last-child {
    border-bottom: none;
  }
`;

const BranchIcon = styled.div<{ theme: any; current?: boolean }>`
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.current ? props.theme.colors.primary : props.theme.colors.textSecondary};
`;

const BranchContent = styled.div`
  flex: 1;
  min-width: 0;
`;

const BranchName = styled.div<{ theme: any; current?: boolean }>`
  font-weight: ${props => props.current ? '600' : '400'};
  color: ${props => props.current ? props.theme.colors.primary : props.theme.colors.text};
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const BranchMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.8rem;
`;

const BranchStatus = styled.div<{ theme: any }>`
  color: ${props => props.theme.colors.textSecondary};
`;

const BranchTracking = styled.div<{ theme: any }>`
  color: ${props => props.theme.colors.textMuted};
`;

const BranchActions = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.75rem;
  opacity: 0;
  transition: opacity 0.2s ease;

  ${BranchItem}:hover & {
    opacity: 1;
  }
`;

const ActionButton = styled.button<{ theme: any; danger?: boolean }>`
  padding: 0.25rem;
  background: transparent;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 3px;
  color: ${props => props.danger ? props.theme.colors.error : props.theme.colors.textSecondary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.danger ? props.theme.colors.error + '20' : props.theme.colors.surfaceHover};
    border-color: ${props => props.danger ? props.theme.colors.error : props.theme.colors.primary};
    color: ${props => props.danger ? props.theme.colors.error : props.theme.colors.primary};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const EmptyState = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${props => props.theme.colors.textMuted};
  text-align: center;
`;

interface BranchListProps {}

const BranchList: React.FC<BranchListProps> = () => {
  const { branches, checkoutBranch, deleteBranch, mergeBranch, isLoading } = useGit();
  const { theme } = useTheme();
  const [selectedBranch, setSelectedBranch] = useState<string | null>(null);

  const localBranches = branches.filter(b => !b.isRemote);
  const remoteBranches = branches.filter(b => b.isRemote);
  const currentBranch = branches.find(b => b.isCurrent);

  const handleBranchClick = (branch: Branch) => {
    setSelectedBranch(branch.name);
  };

  const handleCheckout = async (branchName: string, event: React.MouseEvent) => {
    event.stopPropagation();
    try {
      await checkoutBranch(branchName);
    } catch (error) {
      console.error('Failed to checkout branch:', error);
    }
  };

  const handleDelete = async (branchName: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (window.confirm(`Are you sure you want to delete branch "${branchName}"?`)) {
      try {
        await deleteBranch(branchName);
      } catch (error) {
        console.error('Failed to delete branch:', error);
      }
    }
  };

  const handleMerge = async (branchName: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (window.confirm(`Merge "${branchName}" into "${currentBranch?.name}"?`)) {
      try {
        await mergeBranch(branchName);
      } catch (error) {
        console.error('Failed to merge branch:', error);
      }
    }
  };

  const handleCreateBranch = () => {
    const branchName = window.prompt('Enter branch name:');
    if (branchName) {
      // TODO: Implement create branch
      console.log('Create branch:', branchName);
    }
  };

  const renderBranchItem = (branch: Branch) => {
    const isCurrent = branch.isCurrent;
    const isSelected = selectedBranch === branch.name;

    return (
      <BranchItem
        key={branch.name}
        theme={theme}
        current={isCurrent}
        onClick={() => handleBranchClick(branch)}
      >
        <BranchIcon theme={theme} current={isCurrent}>
          {isCurrent ? <CheckCircle size={16} /> : <GitBranch size={16} />}
        </BranchIcon>
        
        <BranchContent>
          <BranchName theme={theme} current={isCurrent}>
            {branch.name}
          </BranchName>
          
          <BranchMeta>
            {branch.isTracking && branch.upstream && (
              <BranchTracking theme={theme}>
                tracking {branch.upstream}
              </BranchTracking>
            )}
            
            {(branch.ahead > 0 || branch.behind > 0) && (
              <BranchStatus theme={theme}>
                {branch.ahead > 0 && `↑${branch.ahead}`}
                {branch.behind > 0 && `↓${branch.behind}`}
              </BranchStatus>
            )}
          </BranchMeta>
        </BranchContent>

        {!branch.isRemote && (
          <BranchActions theme={theme}>
            {!isCurrent && (
              <>
                <ActionButton
                  theme={theme}
                  onClick={(e) => handleCheckout(branch.name, e)}
                  title="Checkout branch"
                  disabled={isLoading}
                >
                  <CheckCircle size={14} />
                </ActionButton>
                <ActionButton
                  theme={theme}
                  onClick={(e) => handleMerge(branch.name, e)}
                  title="Merge branch"
                  disabled={isLoading}
                >
                  <GitMerge size={14} />
                </ActionButton>
                <ActionButton
                  theme={theme}
                  danger
                  onClick={(e) => handleDelete(branch.name, e)}
                  title="Delete branch"
                  disabled={isLoading}
                >
                  <Trash2 size={14} />
                </ActionButton>
              </>
            )}
            <ActionButton theme={theme} title="More actions">
              <MoreHorizontal size={14} />
            </ActionButton>
          </BranchActions>
        )}
      </BranchItem>
    );
  };

  return (
    <BranchListContainer>
      <BranchListHeader theme={theme}>
        <BranchCount theme={theme}>
          {branches.length} branches
        </BranchCount>
        <CreateBranchButton theme={theme} onClick={handleCreateBranch}>
          <Plus size={16} />
          New Branch
        </CreateBranchButton>
      </BranchListHeader>
      
      <BranchListContent>
        {localBranches.length > 0 && (
          <BranchSection theme={theme}>
            <SectionHeader theme={theme}>Local Branches</SectionHeader>
            {localBranches.map(renderBranchItem)}
          </BranchSection>
        )}

        {remoteBranches.length > 0 && (
          <BranchSection theme={theme}>
            <SectionHeader theme={theme}>Remote Branches</SectionHeader>
            {remoteBranches.map(renderBranchItem)}
          </BranchSection>
        )}

        {branches.length === 0 && (
          <EmptyState theme={theme}>
            <GitBranch size={48} />
            <h3>No branches found</h3>
            <p>Create your first branch to get started.</p>
          </EmptyState>
        )}
      </BranchListContent>
    </BranchListContainer>
  );
};

export default BranchList;
