import React from 'react';
import styled from 'styled-components';
import { 
  GitBranch, 
  GitCommit, 
  Tag, 
  Archive, 
  Globe, 
  FileText,
  RefreshCw,
  Settings
} from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useGit } from '../../contexts/GitContext';
import { useTheme } from '../../contexts/ThemeContext';

const SidebarContainer = styled.div<{ theme: any }>`
  width: 250px;
  background: ${props => props.theme.colors.surface};
  border-right: 1px solid ${props => props.theme.colors.border};
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const SidebarHeader = styled.div<{ theme: any }>`
  padding: 1rem;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.background};
`;

const RepoName = styled.h2<{ theme: any }>`
  font-size: 1.1rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const RepoPath = styled.p<{ theme: any }>`
  font-size: 0.8rem;
  color: ${props => props.theme.colors.textMuted};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const CurrentBranch = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: ${props => props.theme.colors.surface};
  border-radius: 4px;
  font-size: 0.9rem;
  color: ${props => props.theme.colors.primary};
`;

const SidebarContent = styled.div`
  flex: 1;
  overflow-y: auto;
`;

const NavSection = styled.div<{ theme: any }>`
  padding: 1rem 0;
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const NavItem = styled.button<{ theme: any; active?: boolean }>`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: ${props => props.active ? props.theme.colors.primary + '20' : 'transparent'};
  border: none;
  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text};
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;

  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
  }

  &:focus {
    outline: 2px solid ${props => props.theme.colors.primary};
    outline-offset: -2px;
  }
`;

const NavIcon = styled.div`
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const NavLabel = styled.span`
  flex: 1;
  text-align: left;
`;

const NavBadge = styled.span<{ theme: any }>`
  background: ${props => props.theme.colors.primary};
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
`;

const SidebarFooter = styled.div<{ theme: any }>`
  padding: 1rem;
  border-top: 1px solid ${props => props.theme.colors.border};
  display: flex;
  gap: 0.5rem;
`;

const FooterButton = styled.button<{ theme: any }>`
  flex: 1;
  padding: 0.5rem;
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 4px;
  color: ${props => props.theme.colors.text};
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.8rem;

  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
    border-color: ${props => props.theme.colors.primary};
  }

  &:focus {
    outline: 2px solid ${props => props.theme.colors.primary};
    outline-offset: 2px;
  }
`;

interface SidebarProps {}

const Sidebar: React.FC<SidebarProps> = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { 
    currentRepository, 
    branches, 
    tags, 
    remotes, 
    status,
    refreshRepository,
    isLoading 
  } = useGit();
  const { theme } = useTheme();

  const currentBranch = branches.find(b => b.isCurrent);
  const changedFiles = status?.files.length || 0;

  const navItems = [
    {
      path: '/commits',
      icon: GitCommit,
      label: 'Commits',
      badge: null
    },
    {
      path: '/branches',
      icon: GitBranch,
      label: 'Branches',
      badge: branches.length
    },
    {
      path: '/tags',
      icon: Tag,
      label: 'Tags',
      badge: tags.length
    },
    {
      path: '/stashes',
      icon: Archive,
      label: 'Stashes',
      badge: null
    },
    {
      path: '/remotes',
      icon: Globe,
      label: 'Remotes',
      badge: remotes.length
    }
  ];

  const handleNavClick = (path: string) => {
    navigate(path);
  };

  const handleRefresh = async () => {
    await refreshRepository();
  };

  const handleSettings = () => {
    // TODO: Open settings
    console.log('Open settings');
  };

  if (!currentRepository) {
    return null;
  }

  return (
    <SidebarContainer theme={theme}>
      <SidebarHeader theme={theme}>
        <RepoName theme={theme} title={currentRepository.name}>
          {currentRepository.name}
        </RepoName>
        <RepoPath theme={theme} title={currentRepository.path}>
          {currentRepository.path}
        </RepoPath>
        {currentBranch && (
          <CurrentBranch theme={theme}>
            <GitBranch size={16} />
            {currentBranch.name}
          </CurrentBranch>
        )}
      </SidebarHeader>

      <SidebarContent>
        <NavSection theme={theme}>
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            
            return (
              <NavItem
                key={item.path}
                theme={theme}
                active={isActive}
                onClick={() => handleNavClick(item.path)}
              >
                <NavIcon>
                  <Icon size={18} />
                </NavIcon>
                <NavLabel>{item.label}</NavLabel>
                {item.badge !== null && item.badge > 0 && (
                  <NavBadge theme={theme}>{item.badge}</NavBadge>
                )}
              </NavItem>
            );
          })}
        </NavSection>

        {changedFiles > 0 && (
          <NavSection theme={theme}>
            <NavItem
              theme={theme}
              onClick={() => navigate('/changes')}
            >
              <NavIcon>
                <FileText size={18} />
              </NavIcon>
              <NavLabel>Changes</NavLabel>
              <NavBadge theme={theme}>{changedFiles}</NavBadge>
            </NavItem>
          </NavSection>
        )}
      </SidebarContent>

      <SidebarFooter theme={theme}>
        <FooterButton
          theme={theme}
          onClick={handleRefresh}
          disabled={isLoading}
          title="Refresh repository"
        >
          <RefreshCw size={14} className={isLoading ? 'animate-spin' : ''} />
          Refresh
        </FooterButton>
        <FooterButton
          theme={theme}
          onClick={handleSettings}
          title="Settings"
        >
          <Settings size={14} />
        </FooterButton>
      </SidebarFooter>
    </SidebarContainer>
  );
};

export default Sidebar;
