import React from 'react';
import { FolderOpen, GitBranch } from 'lucide-react';
import { useGit } from '../../contexts/GitContext';

const containerStyle = {
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  justifyContent: 'center',
  height: '100vh',
  padding: '2rem',
  textAlign: 'center' as const
};

const logoStyle = {
  fontSize: '3rem',
  fontWeight: 'bold',
  marginBottom: '1rem',
  color: '#007acc',
  display: 'flex',
  alignItems: 'center',
  gap: '1rem'
};

const subtitleStyle = {
  fontSize: '1.2rem',
  color: '#cccccc',
  marginBottom: '3rem',
  maxWidth: '600px'
};

const buttonStyle = {
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  padding: '2rem',
  backgroundColor: '#2d2d2d',
  border: '1px solid #404040',
  borderRadius: '8px',
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  color: '#ffffff',
  minWidth: '250px'
};

const iconStyle = {
  width: '48px',
  height: '48px',
  marginBottom: '1rem',
  color: '#007acc'
};

const titleStyle = {
  fontSize: '1.1rem',
  fontWeight: '600',
  marginBottom: '0.5rem'
};

const descriptionStyle = {
  fontSize: '0.9rem',
  color: '#cccccc',
  lineHeight: '1.4'
};

interface RepositorySelectorProps {}

const RepositorySelector: React.FC<RepositorySelectorProps> = () => {
  const { openRepository } = useGit();

  const handleOpenRepository = async () => {
    const { ipcRenderer } = window.require('electron');
    const path = await ipcRenderer.invoke('select-directory');
    if (path) {
      await openRepository(path);
    }
  };

  return (
    <div style={containerStyle}>
      <div style={logoStyle}>
        <GitBranch size={48} />
        Git Branch Visualizer
      </div>
      
      <p style={subtitleStyle}>
        Select a Git repository to visualize its branch structure
      </p>

      <button 
        style={buttonStyle} 
        onClick={handleOpenRepository}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = '#3d3d3d';
          e.currentTarget.style.borderColor = '#007acc';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = '#2d2d2d';
          e.currentTarget.style.borderColor = '#404040';
          e.currentTarget.style.transform = 'translateY(0)';
        }}
      >
        <div style={iconStyle}>
          <FolderOpen size={48} />
        </div>
        <h3 style={titleStyle}>Open Repository</h3>
        <p style={descriptionStyle}>
          Select a Git repository folder from your computer
        </p>
      </button>
    </div>
  );
};

export default RepositorySelector;
