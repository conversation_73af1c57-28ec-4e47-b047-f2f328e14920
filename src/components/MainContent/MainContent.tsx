import React from 'react';
import styled from 'styled-components';
import CommitList from '../CommitList/CommitList';
import BranchList from '../BranchList/BranchList';
import TagList from '../TagList/TagList';
import RemoteList from '../RemoteList/RemoteList';
import StashList from '../StashList/StashList';
import { useTheme } from '../../contexts/ThemeContext';

const MainContainer = styled.div<{ theme: any }>`
  flex: 1;
  display: flex;
  flex-direction: column;
  background: ${props => props.theme.colors.background};
  overflow: hidden;
`;

const ContentHeader = styled.div<{ theme: any }>`
  padding: 1rem;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.theme.colors.surface};
`;

const ContentTitle = styled.h1<{ theme: any }>`
  font-size: 1.2rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  margin: 0;
`;

const ContentBody = styled.div`
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
`;

interface MainContentProps {
  activeTab?: string;
}

const MainContent: React.FC<MainContentProps> = ({ activeTab = 'commits' }) => {
  const { theme } = useTheme();

  const getTitle = () => {
    switch (activeTab) {
      case 'commits':
        return 'Commit History';
      case 'branches':
        return 'Branches';
      case 'tags':
        return 'Tags';
      case 'stashes':
        return 'Stashes';
      case 'remotes':
        return 'Remotes';
      default:
        return 'Repository';
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'commits':
        return <CommitList />;
      case 'branches':
        return <BranchList />;
      case 'tags':
        return <TagList />;
      case 'stashes':
        return <StashList />;
      case 'remotes':
        return <RemoteList />;
      default:
        return <CommitList />;
    }
  };

  return (
    <MainContainer theme={theme}>
      <ContentHeader theme={theme}>
        <ContentTitle theme={theme}>{getTitle()}</ContentTitle>
      </ContentHeader>
      <ContentBody>
        {renderContent()}
      </ContentBody>
    </MainContainer>
  );
};

export default MainContent;
