import React, { useEffect, useRef, useState } from 'react';
import { GitBranch, FolderOpen, RefreshCw } from 'lucide-react';
import { useGit } from '../../contexts/GitContext';
import { Branch, Commit } from '../../types/git';

const containerStyle = {
  display: 'flex',
  flexDirection: 'column' as const,
  height: '100vh',
  backgroundColor: '#1e1e1e',
  color: '#ffffff'
};

const headerStyle = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: '1rem',
  backgroundColor: '#2d2d2d',
  borderBottom: '1px solid #404040'
};

const repoInfoStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: '1rem'
};

const repoNameStyle = {
  fontSize: '1.2rem',
  fontWeight: '600',
  color: '#ffffff'
};

const repoPathStyle = {
  fontSize: '0.9rem',
  color: '#999999'
};

const actionsStyle = {
  display: 'flex',
  gap: '0.5rem'
};

const buttonStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: '0.5rem',
  padding: '0.5rem 1rem',
  backgroundColor: '#007acc',
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  cursor: 'pointer',
  fontSize: '0.9rem',
  transition: 'background 0.2s ease'
};

const contentStyle = {
  flex: 1,
  display: 'flex',
  overflow: 'hidden'
};

const sidebarStyle = {
  width: '300px',
  backgroundColor: '#2d2d2d',
  borderRight: '1px solid #404040',
  display: 'flex',
  flexDirection: 'column' as const,
  overflow: 'hidden'
};

const sidebarHeaderStyle = {
  padding: '1rem',
  borderBottom: '1px solid #404040',
  backgroundColor: '#1e1e1e'
};

const sidebarTitleStyle = {
  fontSize: '1.1rem',
  fontWeight: '600',
  marginBottom: '0.5rem'
};

const branchCountStyle = {
  fontSize: '0.9rem',
  color: '#cccccc'
};

const branchListStyle = {
  flex: 1,
  overflowY: 'auto' as const,
  padding: '0.5rem'
};

const branchItemStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: '0.75rem',
  padding: '0.75rem',
  margin: '0.25rem 0',
  backgroundColor: '#3d3d3d',
  borderRadius: '6px',
  cursor: 'pointer',
  transition: 'all 0.2s ease'
};

const currentBranchStyle = {
  ...branchItemStyle,
  backgroundColor: '#007acc20',
  borderLeft: '3px solid #007acc'
};

const branchIconStyle = {
  color: '#007acc'
};

const branchNameStyle = {
  flex: 1,
  fontWeight: '500'
};

const visualizerStyle = {
  flex: 1,
  padding: '1rem',
  overflow: 'auto'
};

const canvasContainerStyle = {
  width: '100%',
  height: '100%',
  position: 'relative' as const,
  backgroundColor: '#1e1e1e',
  border: '1px solid #404040',
  borderRadius: '8px'
};

const canvasStyle = {
  width: '100%',
  height: '100%',
  display: 'block'
};

interface BranchVisualizerProps {}

const BranchVisualizer: React.FC<BranchVisualizerProps> = () => {
  const { 
    currentRepository, 
    branches, 
    commits,
    fetchBranches, 
    fetchCommits,
    openRepository,
    isLoading 
  } = useGit();
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [selectedBranch, setSelectedBranch] = useState<string | null>(null);

  useEffect(() => {
    if (currentRepository) {
      fetchBranches();
      fetchCommits();
    }
  }, [currentRepository, fetchBranches, fetchCommits]);

  useEffect(() => {
    if (branches.length > 0 && commits.length > 0) {
      drawBranchGraph();
    }
  }, [branches, commits, selectedBranch]);

  const drawBranchGraph = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Clear canvas
    ctx.fillStyle = '#1e1e1e';
    ctx.fillRect(0, 0, rect.width, rect.height);

    // Draw branch visualization
    const localBranches = branches.filter(b => !b.isRemote);
    const branchHeight = 60;
    const commitWidth = 80;
    const startX = 50;
    const startY = 50;

    // Colors for different branches
    const branchColors = [
      '#007acc', '#28a745', '#ffc107', '#dc3545', '#17a2b8', 
      '#6f42c1', '#fd7e14', '#20c997', '#e83e8c', '#6c757d'
    ];

    localBranches.forEach((branch, branchIndex) => {
      const y = startY + branchIndex * branchHeight;
      const color = branchColors[branchIndex % branchColors.length];
      const isSelected = selectedBranch === branch.name;
      const isCurrent = branch.isCurrent;

      // Draw branch line
      ctx.strokeStyle = isSelected ? '#ffffff' : color;
      ctx.lineWidth = isCurrent ? 4 : 2;
      ctx.beginPath();
      ctx.moveTo(startX, y);
      ctx.lineTo(startX + 800, y);
      ctx.stroke();

      // Draw branch name
      ctx.fillStyle = isCurrent ? '#ffffff' : color;
      ctx.font = isCurrent ? 'bold 14px sans-serif' : '12px sans-serif';
      ctx.fillText(branch.name, startX - 40, y - 10);

      // Draw commits for this branch (simplified)
      const branchCommits = commits.slice(0, 10); // Show first 10 commits
      branchCommits.forEach((commit, commitIndex) => {
        const x = startX + commitIndex * commitWidth;
        
        // Draw commit circle
        ctx.fillStyle = isSelected ? '#ffffff' : color;
        ctx.beginPath();
        ctx.arc(x, y, isCurrent ? 8 : 6, 0, 2 * Math.PI);
        ctx.fill();

        // Draw commit hash
        if (isSelected || isCurrent) {
          ctx.fillStyle = '#cccccc';
          ctx.font = '10px monospace';
          ctx.fillText(commit.shortHash, x - 15, y + 20);
        }
      });

      // Draw merge lines (simplified)
      if (branchIndex > 0) {
        const parentY = startY + (branchIndex - 1) * branchHeight;
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;
        ctx.setLineDash([5, 5]);
        ctx.beginPath();
        ctx.moveTo(startX + 200, y);
        ctx.lineTo(startX + 200, parentY);
        ctx.stroke();
        ctx.setLineDash([]);
      }
    });

    // Draw legend
    ctx.fillStyle = '#ffffff';
    ctx.font = '12px sans-serif';
    ctx.fillText('Branch Visualization', 20, rect.height - 40);
    ctx.fillStyle = '#cccccc';
    ctx.font = '10px sans-serif';
    ctx.fillText('● Current branch (bold) | ○ Other branches | Click branch name to highlight', 20, rect.height - 20);
  };

  const handleBranchClick = (branchName: string) => {
    setSelectedBranch(selectedBranch === branchName ? null : branchName);
  };

  const handleOpenRepository = async () => {
    const { ipcRenderer } = window.require('electron');
    const path = await ipcRenderer.invoke('select-directory');
    if (path) {
      await openRepository(path);
    }
  };

  const handleRefresh = async () => {
    if (currentRepository) {
      await fetchBranches();
      await fetchCommits();
    }
  };

  if (!currentRepository) {
    return null;
  }

  const localBranches = branches.filter(b => !b.isRemote);
  const currentBranch = branches.find(b => b.isCurrent);

  return (
    <div style={containerStyle}>
      <div style={headerStyle}>
        <div style={repoInfoStyle}>
          <GitBranch size={24} />
          <div>
            <div style={repoNameStyle}>{currentRepository.name}</div>
            <div style={repoPathStyle}>{currentRepository.path}</div>
          </div>
        </div>
        
        <div style={actionsStyle}>
          <button 
            style={buttonStyle}
            onClick={handleRefresh}
            disabled={isLoading}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#1a8cff'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#007acc'}
          >
            <RefreshCw size={16} />
            Refresh
          </button>
          <button 
            style={buttonStyle}
            onClick={handleOpenRepository}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#1a8cff'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#007acc'}
          >
            <FolderOpen size={16} />
            Open Other
          </button>
        </div>
      </div>

      <div style={contentStyle}>
        <div style={sidebarStyle}>
          <div style={sidebarHeaderStyle}>
            <div style={sidebarTitleStyle}>Branches</div>
            <div style={branchCountStyle}>
              {localBranches.length} local branches
            </div>
            {currentBranch && (
              <div style={{ marginTop: '0.5rem', color: '#007acc', fontSize: '0.9rem' }}>
                Current: {currentBranch.name}
              </div>
            )}
          </div>
          
          <div style={branchListStyle}>
            {localBranches.map((branch) => (
              <div
                key={branch.name}
                style={branch.isCurrent ? currentBranchStyle : branchItemStyle}
                onClick={() => handleBranchClick(branch.name)}
                onMouseEnter={(e) => {
                  if (!branch.isCurrent) {
                    e.currentTarget.style.backgroundColor = '#4d4d4d';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!branch.isCurrent) {
                    e.currentTarget.style.backgroundColor = '#3d3d3d';
                  }
                }}
              >
                <GitBranch size={16} style={branchIconStyle} />
                <span style={branchNameStyle}>{branch.name}</span>
                {branch.isCurrent && (
                  <span style={{ fontSize: '0.8rem', color: '#007acc' }}>●</span>
                )}
              </div>
            ))}
          </div>
        </div>

        <div style={visualizerStyle}>
          <div style={canvasContainerStyle}>
            <canvas
              ref={canvasRef}
              style={canvasStyle}
              onClick={(e) => {
                // Handle canvas clicks for interactive features
                const rect = e.currentTarget.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                console.log('Canvas clicked at:', x, y);
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BranchVisualizer;
