import React, { useState } from 'react';
import styled from 'styled-components';
import { FolderOpen, GitBranch, Download, Settings } from 'lucide-react';
import { useGit } from '../../contexts/GitContext';
import { useTheme } from '../../contexts/ThemeContext';

const WelcomeContainer = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  padding: 2rem;
`;

const Logo = styled.div<{ theme: any }>`
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 1rem;
  color: ${props => props.theme.colors.primary};
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const Subtitle = styled.p<{ theme: any }>`
  font-size: 1.2rem;
  color: ${props => props.theme.colors.textSecondary};
  margin-bottom: 3rem;
  text-align: center;
  max-width: 600px;
`;

const ActionGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  width: 100%;
`;

const ActionCard = styled.button<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: ${props => props.theme.colors.text};

  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
    border-color: ${props => props.theme.colors.primary};
    transform: translateY(-2px);
  }

  &:focus {
    outline: 2px solid ${props => props.theme.colors.primary};
    outline-offset: 2px;
  }
`;

const ActionIcon = styled.div<{ theme: any }>`
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  color: ${props => props.theme.colors.primary};
`;

const ActionTitle = styled.h3`
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
`;

const ActionDescription = styled.p<{ theme: any }>`
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
  text-align: center;
  line-height: 1.4;
`;

const RecentRepos = styled.div<{ theme: any }>`
  margin-top: 3rem;
  width: 100%;
  max-width: 600px;
`;

const RecentTitle = styled.h3<{ theme: any }>`
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: ${props => props.theme.colors.text};
`;

const RecentList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const RecentItem = styled.button<{ theme: any }>`
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background: ${props => props.theme.colors.surface};
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: ${props => props.theme.colors.text};
  text-align: left;

  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
    border-color: ${props => props.theme.colors.primary};
  }
`;

const RepoIcon = styled.div<{ theme: any }>`
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  color: ${props => props.theme.colors.primary};
`;

const RepoInfo = styled.div`
  flex: 1;
`;

const RepoName = styled.div`
  font-weight: 500;
  margin-bottom: 0.25rem;
`;

const RepoPath = styled.div<{ theme: any }>`
  font-size: 0.8rem;
  color: ${props => props.theme.colors.textMuted};
`;

interface WelcomeScreenProps {}

const WelcomeScreen: React.FC<WelcomeScreenProps> = () => {
  const { openRepository } = useGit();
  const { theme } = useTheme();
  const [recentRepos] = useState<Array<{ name: string; path: string }>>([
    // This would come from localStorage or settings
  ]);

  const handleOpenRepository = async () => {
    const { ipcRenderer } = window.require('electron');
    const path = await ipcRenderer.invoke('select-directory');
    if (path) {
      await openRepository(path);
    }
  };

  const handleCloneRepository = () => {
    // TODO: Implement clone dialog
    console.log('Clone repository');
  };

  const handleOpenSettings = () => {
    // TODO: Implement settings dialog
    console.log('Open settings');
  };

  const handleOpenRecent = async (path: string) => {
    await openRepository(path);
  };

  return (
    <WelcomeContainer theme={theme}>
      <Logo theme={theme}>
        <GitBranch size={48} />
        SourceGit Electron
      </Logo>
      
      <Subtitle theme={theme}>
        A modern Git GUI client built with Electron and TypeScript.
        Fast, intuitive, and cross-platform.
      </Subtitle>

      <ActionGrid>
        <ActionCard theme={theme} onClick={handleOpenRepository}>
          <ActionIcon theme={theme}>
            <FolderOpen size={48} />
          </ActionIcon>
          <ActionTitle>Open Repository</ActionTitle>
          <ActionDescription theme={theme}>
            Open an existing Git repository from your local machine
          </ActionDescription>
        </ActionCard>

        <ActionCard theme={theme} onClick={handleCloneRepository}>
          <ActionIcon theme={theme}>
            <Download size={48} />
          </ActionIcon>
          <ActionTitle>Clone Repository</ActionTitle>
          <ActionDescription theme={theme}>
            Clone a repository from a remote URL (GitHub, GitLab, etc.)
          </ActionDescription>
        </ActionCard>

        <ActionCard theme={theme} onClick={handleOpenSettings}>
          <ActionIcon theme={theme}>
            <Settings size={48} />
          </ActionIcon>
          <ActionTitle>Settings</ActionTitle>
          <ActionDescription theme={theme}>
            Configure Git settings, themes, and application preferences
          </ActionDescription>
        </ActionCard>
      </ActionGrid>

      {recentRepos.length > 0 && (
        <RecentRepos theme={theme}>
          <RecentTitle theme={theme}>Recent Repositories</RecentTitle>
          <RecentList>
            {recentRepos.map((repo, index) => (
              <RecentItem
                key={index}
                theme={theme}
                onClick={() => handleOpenRecent(repo.path)}
              >
                <RepoIcon theme={theme}>
                  <GitBranch size={20} />
                </RepoIcon>
                <RepoInfo>
                  <RepoName>{repo.name}</RepoName>
                  <RepoPath theme={theme}>{repo.path}</RepoPath>
                </RepoInfo>
              </RecentItem>
            ))}
          </RecentList>
        </RecentRepos>
      )}
    </WelcomeContainer>
  );
};

export default WelcomeScreen;
