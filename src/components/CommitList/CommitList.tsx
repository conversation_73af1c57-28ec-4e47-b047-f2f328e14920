import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { FixedSizeList as List } from 'react-window';
import { GitCommit, User, Calendar, Hash, Copy, MoreHorizontal } from 'lucide-react';
import { useGit } from '../../contexts/GitContext';
import { useTheme } from '../../contexts/ThemeContext';
import { Commit } from '../../types/git';

const CommitListContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const CommitListHeader = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
`;

const CommitCount = styled.span<{ theme: any }>`
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
`;

const LoadMoreButton = styled.button<{ theme: any }>`
  padding: 0.5rem 1rem;
  background: ${props => props.theme.colors.primary};
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.primaryHover};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const CommitListContent = styled.div`
  flex: 1;
  overflow: hidden;
`;

const CommitItem = styled.div<{ theme: any; selected?: boolean }>`
  display: flex;
  align-items: flex-start;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid ${props => props.theme.colors.border};
  background: ${props => props.selected ? props.theme.colors.primary + '10' : 'transparent'};
  cursor: pointer;
  transition: background 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
  }
`;

const CommitGraph = styled.div<{ theme: any }>`
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.primary};
`;

const CommitContent = styled.div`
  flex: 1;
  min-width: 0;
`;

const CommitMessage = styled.div<{ theme: any }>`
  font-weight: 500;
  color: ${props => props.theme.colors.text};
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const CommitMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.8rem;
`;

const CommitAuthor = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: ${props => props.theme.colors.textSecondary};
`;

const CommitDate = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: ${props => props.theme.colors.textSecondary};
`;

const CommitHash = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: ${props => props.theme.colors.textMuted};
  font-family: monospace;
`;

const CommitRefs = styled.div`
  display: flex;
  gap: 0.25rem;
  margin-left: 0.5rem;
`;

const CommitRef = styled.span<{ theme: any; type: 'branch' | 'tag' | 'remote' }>`
  padding: 0.1rem 0.4rem;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 500;
  background: ${props => {
    switch (props.type) {
      case 'branch':
        return props.theme.colors.success + '20';
      case 'tag':
        return props.theme.colors.warning + '20';
      case 'remote':
        return props.theme.colors.info + '20';
      default:
        return props.theme.colors.secondary + '20';
    }
  }};
  color: ${props => {
    switch (props.type) {
      case 'branch':
        return props.theme.colors.success;
      case 'tag':
        return props.theme.colors.warning;
      case 'remote':
        return props.theme.colors.info;
      default:
        return props.theme.colors.secondary;
    }
  }};
`;

const CommitActions = styled.div<{ theme: any }>`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.75rem;
  opacity: 0;
  transition: opacity 0.2s ease;

  ${CommitItem}:hover & {
    opacity: 1;
  }
`;

const ActionButton = styled.button<{ theme: any }>`
  padding: 0.25rem;
  background: transparent;
  border: 1px solid ${props => props.theme.colors.border};
  border-radius: 3px;
  color: ${props => props.theme.colors.textSecondary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.theme.colors.surfaceHover};
    border-color: ${props => props.theme.colors.primary};
    color: ${props => props.theme.colors.primary};
  }
`;

const EmptyState = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: ${props => props.theme.colors.textMuted};
  text-align: center;
`;

const EmptyIcon = styled.div<{ theme: any }>`
  width: 64px;
  height: 64px;
  margin-bottom: 1rem;
  color: ${props => props.theme.colors.textMuted};
`;

interface CommitListProps {}

const CommitList: React.FC<CommitListProps> = () => {
  const { commits, fetchCommits, isLoading } = useGit();
  const { theme } = useTheme();
  const [selectedCommit, setSelectedCommit] = useState<string | null>(null);
  const [limit, setLimit] = useState(100);

  useEffect(() => {
    if (commits.length === 0) {
      fetchCommits(undefined, limit);
    }
  }, [fetchCommits, limit, commits.length]);

  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60));
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60));
        return `${minutes}m ago`;
      }
      return `${hours}h ago`;
    } else if (days < 7) {
      return `${days}d ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const parseRefs = (refs: string[]) => {
    return refs.map(ref => {
      if (ref.startsWith('origin/')) {
        return { name: ref, type: 'remote' as const };
      } else if (ref.startsWith('tag: ')) {
        return { name: ref.replace('tag: ', ''), type: 'tag' as const };
      } else {
        return { name: ref, type: 'branch' as const };
      }
    });
  };

  const handleCommitClick = (commit: Commit) => {
    setSelectedCommit(commit.hash);
    // TODO: Show commit details
  };

  const handleCopyHash = (hash: string, event: React.MouseEvent) => {
    event.stopPropagation();
    navigator.clipboard.writeText(hash);
    // TODO: Show toast notification
  };

  const handleLoadMore = () => {
    const newLimit = limit + 100;
    setLimit(newLimit);
    fetchCommits(undefined, newLimit);
  };

  const renderCommitItem = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    const commit = commits[index];
    const refs = parseRefs(commit.refs);
    const isSelected = selectedCommit === commit.hash;

    return (
      <div style={style}>
        <CommitItem
          theme={theme}
          selected={isSelected}
          onClick={() => handleCommitClick(commit)}
        >
          <CommitGraph theme={theme}>
            <GitCommit size={16} />
          </CommitGraph>
          
          <CommitContent>
            <CommitMessage theme={theme}>
              {commit.shortMessage}
              {refs.length > 0 && (
                <CommitRefs>
                  {refs.map((ref, i) => (
                    <CommitRef key={i} theme={theme} type={ref.type}>
                      {ref.name}
                    </CommitRef>
                  ))}
                </CommitRefs>
              )}
            </CommitMessage>
            
            <CommitMeta>
              <CommitAuthor theme={theme}>
                <User size={12} />
                {commit.author.name}
              </CommitAuthor>
              
              <CommitDate theme={theme}>
                <Calendar size={12} />
                {formatDate(commit.author.date)}
              </CommitDate>
              
              <CommitHash theme={theme}>
                <Hash size={12} />
                {commit.shortHash}
              </CommitHash>
            </CommitMeta>
          </CommitContent>

          <CommitActions theme={theme}>
            <ActionButton
              theme={theme}
              onClick={(e) => handleCopyHash(commit.hash, e)}
              title="Copy commit hash"
            >
              <Copy size={14} />
            </ActionButton>
            <ActionButton theme={theme} title="More actions">
              <MoreHorizontal size={14} />
            </ActionButton>
          </CommitActions>
        </CommitItem>
      </div>
    );
  };

  if (commits.length === 0 && !isLoading) {
    return (
      <CommitListContainer>
        <EmptyState theme={theme}>
          <EmptyIcon theme={theme}>
            <GitCommit size={64} />
          </EmptyIcon>
          <h3>No commits found</h3>
          <p>This repository doesn't have any commits yet.</p>
        </EmptyState>
      </CommitListContainer>
    );
  }

  return (
    <CommitListContainer>
      <CommitListHeader theme={theme}>
        <CommitCount theme={theme}>
          {commits.length} commits
        </CommitCount>
        <LoadMoreButton
          theme={theme}
          onClick={handleLoadMore}
          disabled={isLoading}
        >
          {isLoading ? 'Loading...' : 'Load More'}
        </LoadMoreButton>
      </CommitListHeader>
      
      <CommitListContent>
        {commits.length > 0 && (
          <List
            height={400} // This will be dynamically calculated
            itemCount={commits.length}
            itemSize={80}
            width="100%"
          >
            {renderCommitItem}
          </List>
        )}
      </CommitListContent>
    </CommitListContainer>
  );
};

export default CommitList;
