import React, { createContext, useContext, useState, useCallback } from 'react';
import { GitService } from '../services/GitService';
import { Repository, Commit, Branch, Tag, Remote, GitStatus } from '../types/git';

interface GitContextType {
  currentRepository: Repository | null;
  commits: Commit[];
  branches: Branch[];
  tags: Tag[];
  remotes: Remote[];
  status: GitStatus | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  openRepository: (path: string) => Promise<void>;
  closeRepository: () => void;
  refreshRepository: () => Promise<void>;
  fetchCommits: (branch?: string, limit?: number) => Promise<void>;
  fetchBranches: () => Promise<void>;
  fetchTags: () => Promise<void>;
  fetchRemotes: () => Promise<void>;
  fetchStatus: () => Promise<void>;
  
  // Git operations
  createBranch: (name: string, startPoint?: string) => Promise<void>;
  deleteBranch: (name: string, force?: boolean) => Promise<void>;
  checkoutBranch: (name: string) => Promise<void>;
  mergeBranch: (branch: string) => Promise<void>;
  rebaseBranch: (branch: string) => Promise<void>;
  
  fetch: (remote?: string) => Promise<void>;
  pull: (remote?: string, branch?: string) => Promise<void>;
  push: (remote?: string, branch?: string) => Promise<void>;
  
  stageFiles: (files: string[]) => Promise<void>;
  unstageFiles: (files: string[]) => Promise<void>;
  commit: (message: string, files?: string[]) => Promise<void>;
  
  resetHard: (commit: string) => Promise<void>;
  resetSoft: (commit: string) => Promise<void>;
  revert: (commit: string) => Promise<void>;
  cherryPick: (commit: string) => Promise<void>;
}

const GitContext = createContext<GitContextType | undefined>(undefined);

export const useGit = (): GitContextType => {
  const context = useContext(GitContext);
  if (!context) {
    throw new Error('useGit must be used within a GitProvider');
  }
  return context;
};

interface GitProviderProps {
  children: React.ReactNode;
}

export const GitProvider: React.FC<GitProviderProps> = ({ children }) => {
  const [currentRepository, setCurrentRepository] = useState<Repository | null>(null);
  const [commits, setCommits] = useState<Commit[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [remotes, setRemotes] = useState<Remote[]>([]);
  const [status, setStatus] = useState<GitStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const gitService = new GitService();

  const handleError = (err: any) => {
    console.error('Git operation error:', err);
    setError(err.message || 'An error occurred');
    setIsLoading(false);
  };

  const openRepository = useCallback(async (path: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const repo = await gitService.openRepository(path);
      setCurrentRepository(repo);
      
      // Load initial data
      await Promise.all([
        fetchCommits(),
        fetchBranches(),
        fetchTags(),
        fetchRemotes(),
        fetchStatus()
      ]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const closeRepository = useCallback(() => {
    setCurrentRepository(null);
    setCommits([]);
    setBranches([]);
    setTags([]);
    setRemotes([]);
    setStatus(null);
    setError(null);
  }, []);

  const refreshRepository = useCallback(async () => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await Promise.all([
        fetchCommits(),
        fetchBranches(),
        fetchTags(),
        fetchRemotes(),
        fetchStatus()
      ]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository]);

  const fetchCommits = useCallback(async (branch?: string, limit = 100) => {
    if (!currentRepository) return;
    
    try {
      const commitList = await gitService.getCommits(currentRepository.path, branch, limit);
      setCommits(commitList);
    } catch (err) {
      handleError(err);
    }
  }, [currentRepository]);

  const fetchBranches = useCallback(async () => {
    if (!currentRepository) return;
    
    try {
      const branchList = await gitService.getBranches(currentRepository.path);
      setBranches(branchList);
    } catch (err) {
      handleError(err);
    }
  }, [currentRepository]);

  const fetchTags = useCallback(async () => {
    if (!currentRepository) return;
    
    try {
      const tagList = await gitService.getTags(currentRepository.path);
      setTags(tagList);
    } catch (err) {
      handleError(err);
    }
  }, [currentRepository]);

  const fetchRemotes = useCallback(async () => {
    if (!currentRepository) return;
    
    try {
      const remoteList = await gitService.getRemotes(currentRepository.path);
      setRemotes(remoteList);
    } catch (err) {
      handleError(err);
    }
  }, [currentRepository]);

  const fetchStatus = useCallback(async () => {
    if (!currentRepository) return;
    
    try {
      const repoStatus = await gitService.getStatus(currentRepository.path);
      setStatus(repoStatus);
    } catch (err) {
      handleError(err);
    }
  }, [currentRepository]);

  // Git operations
  const createBranch = useCallback(async (name: string, startPoint?: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.createBranch(currentRepository.path, name, startPoint);
      await fetchBranches();
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchBranches]);

  const deleteBranch = useCallback(async (name: string, force = false) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.deleteBranch(currentRepository.path, name, force);
      await fetchBranches();
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchBranches]);

  const checkoutBranch = useCallback(async (name: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.checkoutBranch(currentRepository.path, name);
      await Promise.all([fetchBranches(), fetchStatus(), fetchCommits()]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchBranches, fetchStatus, fetchCommits]);

  const mergeBranch = useCallback(async (branch: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.mergeBranch(currentRepository.path, branch);
      await Promise.all([fetchCommits(), fetchStatus()]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchCommits, fetchStatus]);

  const rebaseBranch = useCallback(async (branch: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.rebaseBranch(currentRepository.path, branch);
      await Promise.all([fetchCommits(), fetchStatus()]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchCommits, fetchStatus]);

  const fetch = useCallback(async (remote?: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.fetch(currentRepository.path, remote);
      await Promise.all([fetchCommits(), fetchBranches(), fetchTags()]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchCommits, fetchBranches, fetchTags]);

  const pull = useCallback(async (remote?: string, branch?: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.pull(currentRepository.path, remote, branch);
      await Promise.all([fetchCommits(), fetchStatus()]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchCommits, fetchStatus]);

  const push = useCallback(async (remote?: string, branch?: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.push(currentRepository.path, remote, branch);
      await fetchBranches();
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchBranches]);

  const stageFiles = useCallback(async (files: string[]) => {
    if (!currentRepository) return;
    
    try {
      await gitService.stageFiles(currentRepository.path, files);
      await fetchStatus();
    } catch (err) {
      handleError(err);
    }
  }, [currentRepository, fetchStatus]);

  const unstageFiles = useCallback(async (files: string[]) => {
    if (!currentRepository) return;
    
    try {
      await gitService.unstageFiles(currentRepository.path, files);
      await fetchStatus();
    } catch (err) {
      handleError(err);
    }
  }, [currentRepository, fetchStatus]);

  const commit = useCallback(async (message: string, files?: string[]) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.commit(currentRepository.path, message, files);
      await Promise.all([fetchCommits(), fetchStatus()]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchCommits, fetchStatus]);

  const resetHard = useCallback(async (commitHash: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.resetHard(currentRepository.path, commitHash);
      await Promise.all([fetchCommits(), fetchStatus()]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchCommits, fetchStatus]);

  const resetSoft = useCallback(async (commitHash: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.resetSoft(currentRepository.path, commitHash);
      await Promise.all([fetchCommits(), fetchStatus()]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchCommits, fetchStatus]);

  const revert = useCallback(async (commitHash: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.revert(currentRepository.path, commitHash);
      await Promise.all([fetchCommits(), fetchStatus()]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchCommits, fetchStatus]);

  const cherryPick = useCallback(async (commitHash: string) => {
    if (!currentRepository) return;
    
    try {
      setIsLoading(true);
      await gitService.cherryPick(currentRepository.path, commitHash);
      await Promise.all([fetchCommits(), fetchStatus()]);
    } catch (err) {
      handleError(err);
    } finally {
      setIsLoading(false);
    }
  }, [currentRepository, fetchCommits, fetchStatus]);

  const value: GitContextType = {
    currentRepository,
    commits,
    branches,
    tags,
    remotes,
    status,
    isLoading,
    error,
    
    openRepository,
    closeRepository,
    refreshRepository,
    fetchCommits,
    fetchBranches,
    fetchTags,
    fetchRemotes,
    fetchStatus,
    
    createBranch,
    deleteBranch,
    checkoutBranch,
    mergeBranch,
    rebaseBranch,
    
    fetch,
    pull,
    push,
    
    stageFiles,
    unstageFiles,
    commit,
    
    resetHard,
    resetSoft,
    revert,
    cherryPick
  };

  return (
    <GitContext.Provider value={value}>
      {children}
    </GitContext.Provider>
  );
};
