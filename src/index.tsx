import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { ThemeProvider } from './contexts/ThemeContext';
import { GitProvider } from './contexts/GitContext';
import './styles/global.css';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <ThemeProvider>
      <GitProvider>
        <App />
      </GitProvider>
    </ThemeProvider>
  </React.StrictMode>
);
