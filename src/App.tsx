import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import styled from 'styled-components';
import Sidebar from './components/Sidebar/Sidebar';
import MainContent from './components/MainContent/MainContent';
import StatusBar from './components/StatusBar/StatusBar';
import WelcomeScreen from './components/WelcomeScreen/WelcomeScreen';
import { useGit } from './contexts/GitContext';
import { useTheme } from './contexts/ThemeContext';

const AppContainer = styled.div<{ theme: any }>`
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
`;

const MainLayout = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`;

const ContentArea = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const App: React.FC = () => {
  const { currentRepository } = useGit();
  const { theme } = useTheme();

  useEffect(() => {
    // Listen for menu events from Electron
    const { ipcRenderer } = window.require('electron');
    
    const handleMenuEvent = (event: string, handler: () => void) => {
      ipcRenderer.on(event, handler);
      return () => ipcRenderer.removeListener(event, handler);
    };

    const cleanupFunctions = [
      handleMenuEvent('menu-open-repository', () => {
        // Handle open repository
        console.log('Open repository requested');
      }),
      handleMenuEvent('menu-clone-repository', () => {
        // Handle clone repository
        console.log('Clone repository requested');
      }),
      handleMenuEvent('menu-preferences', () => {
        // Handle preferences
        console.log('Preferences requested');
      }),
      handleMenuEvent('menu-fetch', () => {
        // Handle fetch
        console.log('Fetch requested');
      }),
      handleMenuEvent('menu-pull', () => {
        // Handle pull
        console.log('Pull requested');
      }),
      handleMenuEvent('menu-push', () => {
        // Handle push
        console.log('Push requested');
      }),
      handleMenuEvent('menu-about', () => {
        // Handle about
        console.log('About requested');
      })
    ];

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, []);

  return (
    <Router>
      <AppContainer theme={theme}>
        {!currentRepository ? (
          <WelcomeScreen />
        ) : (
          <MainLayout>
            <Sidebar />
            <ContentArea>
              <Routes>
                <Route path="/" element={<MainContent />} />
                <Route path="/commits" element={<MainContent activeTab="commits" />} />
                <Route path="/branches" element={<MainContent activeTab="branches" />} />
                <Route path="/tags" element={<MainContent activeTab="tags" />} />
                <Route path="/stashes" element={<MainContent activeTab="stashes" />} />
                <Route path="/remotes" element={<MainContent activeTab="remotes" />} />
              </Routes>
              <StatusBar />
            </ContentArea>
          </MainLayout>
        )}
      </AppContainer>
    </Router>
  );
};

export default App;
