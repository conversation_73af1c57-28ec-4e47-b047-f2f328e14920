import React, { useEffect } from 'react';
import RepositorySelector from './components/RepositorySelector/RepositorySelector';
import BranchVisualizer from './components/BranchVisualizer/BranchVisualizer';
import { useGit } from './contexts/GitContext';

const AppContainer = {
  display: 'flex',
  flexDirection: 'column' as const,
  height: '100vh',
  backgroundColor: '#1e1e1e',
  color: '#ffffff'
};

const App: React.FC = () => {
  const { currentRepository } = useGit();

  return (
    <div style={AppContainer}>
      {!currentRepository ? (
        <RepositorySelector />
      ) : (
        <BranchVisualizer />
      )}
    </div>
  );
};

export default App;
