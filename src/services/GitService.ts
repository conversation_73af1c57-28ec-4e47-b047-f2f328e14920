import simpleGit, { SimpleGit, Log<PERSON><PERSON>ult, StatusResult, BranchSummary } from 'simple-git';
import * as path from 'path';
import * as fs from 'fs';
import { 
  Repository, 
  Commit, 
  Branch, 
  Tag, 
  Remote, 
  GitStatus, 
  FileStatus, 
  Stash,
  FileDiff,
  CommitDiff,
  GitConfig,
  CloneOptions,
  MergeResult,
  RebaseResult,
  GitLogOptions
} from '../types/git';

export class GitService {
  private gitInstances: Map<string, SimpleGit> = new Map();

  private getGitInstance(repoPath: string): SimpleGit {
    if (!this.gitInstances.has(repoPath)) {
      this.gitInstances.set(repoPath, simpleGit(repoPath));
    }
    return this.gitInstances.get(repoPath)!;
  }

  async openRepository(repoPath: string): Promise<Repository> {
    const git = this.getGitInstance(repoPath);
    
    // Verify it's a git repository
    const isRepo = await git.checkIsRepo();
    if (!isRepo) {
      throw new Error(`${repoPath} is not a git repository`);
    }

    const status = await git.status();
    const currentBranch = status.current || 'HEAD';
    const repoName = path.basename(repoPath);

    // Get last commit
    let lastCommit: Commit | undefined;
    try {
      const log = await git.log({ maxCount: 1 });
      if (log.latest) {
        lastCommit = this.parseCommit(log.latest);
      }
    } catch (error) {
      // Repository might be empty
      console.warn('Could not get last commit:', error);
    }

    return {
      name: repoName,
      path: repoPath,
      currentBranch,
      isClean: status.isClean(),
      lastCommit
    };
  }

  async getCommits(repoPath: string, branch?: string, limit = 100): Promise<Commit[]> {
    const git = this.getGitInstance(repoPath);
    const options: any = { maxCount: limit };
    
    if (branch) {
      options.from = branch;
    }

    const log = await git.log(options);
    return log.all.map(commit => this.parseCommit(commit));
  }

  async getBranches(repoPath: string): Promise<Branch[]> {
    const git = this.getGitInstance(repoPath);
    const branchSummary = await git.branch(['-a', '-v']);
    const branches: Branch[] = [];

    // Local branches
    for (const [name, branch] of Object.entries(branchSummary.branches)) {
      if (name.startsWith('remotes/')) continue;
      
      branches.push({
        name,
        fullName: name,
        isRemote: false,
        isCurrent: branch.current,
        isTracking: !!branch.tracking,
        upstream: branch.tracking,
        ahead: 0, // Will be calculated separately if needed
        behind: 0,
        lastCommit: undefined // Will be populated if needed
      });
    }

    // Remote branches
    for (const [name, branch] of Object.entries(branchSummary.branches)) {
      if (!name.startsWith('remotes/')) continue;
      
      const shortName = name.replace('remotes/', '');
      branches.push({
        name: shortName,
        fullName: name,
        isRemote: true,
        isCurrent: false,
        isTracking: false,
        ahead: 0,
        behind: 0,
        lastCommit: undefined
      });
    }

    return branches;
  }

  async getTags(repoPath: string): Promise<Tag[]> {
    const git = this.getGitInstance(repoPath);
    const tagList = await git.tags();
    const tags: Tag[] = [];

    for (const tagName of tagList.all) {
      try {
        // Get tag details
        const tagInfo = await git.raw(['show', '--format=%H|%an|%ae|%at|%s', '--no-patch', tagName]);
        const [hash, authorName, authorEmail, timestamp, message] = tagInfo.trim().split('|');
        
        tags.push({
          name: tagName,
          hash,
          message,
          tagger: {
            name: authorName,
            email: authorEmail,
            date: new Date(parseInt(timestamp) * 1000)
          },
          isAnnotated: true // Simplified for now
        });
      } catch (error) {
        // Lightweight tag
        const hash = await git.raw(['rev-list', '-n', '1', tagName]);
        tags.push({
          name: tagName,
          hash: hash.trim(),
          isAnnotated: false
        });
      }
    }

    return tags;
  }

  async getRemotes(repoPath: string): Promise<Remote[]> {
    const git = this.getGitInstance(repoPath);
    const remotes = await git.getRemotes(true);
    
    return remotes.map(remote => ({
      name: remote.name,
      url: remote.refs.fetch,
      pushUrl: remote.refs.push !== remote.refs.fetch ? remote.refs.push : undefined,
      type: 'both' as const
    }));
  }

  async getStatus(repoPath: string): Promise<GitStatus> {
    const git = this.getGitInstance(repoPath);
    const status = await git.status();
    
    const files: FileStatus[] = [];
    
    // Staged files
    status.staged.forEach(file => {
      files.push({
        path: file,
        status: 'modified', // Simplified
        staged: true
      });
    });

    // Modified files
    status.modified.forEach(file => {
      files.push({
        path: file,
        status: 'modified',
        staged: false
      });
    });

    // New files
    status.not_added.forEach(file => {
      files.push({
        path: file,
        status: 'untracked',
        staged: false
      });
    });

    // Deleted files
    status.deleted.forEach(file => {
      files.push({
        path: file,
        status: 'deleted',
        staged: false
      });
    });

    return {
      current: status.current || 'HEAD',
      tracking: status.tracking,
      ahead: status.ahead,
      behind: status.behind,
      files,
      isClean: status.isClean()
    };
  }

  async createBranch(repoPath: string, name: string, startPoint?: string): Promise<void> {
    const git = this.getGitInstance(repoPath);
    if (startPoint) {
      await git.checkoutBranch(name, startPoint);
    } else {
      await git.checkoutLocalBranch(name);
    }
  }

  async deleteBranch(repoPath: string, name: string, force = false): Promise<void> {
    const git = this.getGitInstance(repoPath);
    const flag = force ? '-D' : '-d';
    await git.branch([flag, name]);
  }

  async checkoutBranch(repoPath: string, name: string): Promise<void> {
    const git = this.getGitInstance(repoPath);
    await git.checkout(name);
  }

  async mergeBranch(repoPath: string, branch: string): Promise<MergeResult> {
    const git = this.getGitInstance(repoPath);
    try {
      await git.merge([branch]);
      return { success: true };
    } catch (error: any) {
      const conflicts = await this.getConflictedFiles(repoPath);
      return {
        success: false,
        conflicts,
        message: error.message
      };
    }
  }

  async rebaseBranch(repoPath: string, branch: string): Promise<RebaseResult> {
    const git = this.getGitInstance(repoPath);
    try {
      await git.rebase([branch]);
      return { success: true };
    } catch (error: any) {
      const conflicts = await this.getConflictedFiles(repoPath);
      return {
        success: false,
        conflicts,
        message: error.message
      };
    }
  }

  async fetch(repoPath: string, remote?: string): Promise<void> {
    const git = this.getGitInstance(repoPath);
    if (remote) {
      await git.fetch(remote);
    } else {
      await git.fetch();
    }
  }

  async pull(repoPath: string, remote?: string, branch?: string): Promise<void> {
    const git = this.getGitInstance(repoPath);
    if (remote && branch) {
      await git.pull(remote, branch);
    } else {
      await git.pull();
    }
  }

  async push(repoPath: string, remote?: string, branch?: string): Promise<void> {
    const git = this.getGitInstance(repoPath);
    if (remote && branch) {
      await git.push(remote, branch);
    } else {
      await git.push();
    }
  }

  async stageFiles(repoPath: string, files: string[]): Promise<void> {
    const git = this.getGitInstance(repoPath);
    await git.add(files);
  }

  async unstageFiles(repoPath: string, files: string[]): Promise<void> {
    const git = this.getGitInstance(repoPath);
    await git.reset(['HEAD', ...files]);
  }

  async commit(repoPath: string, message: string, files?: string[]): Promise<void> {
    const git = this.getGitInstance(repoPath);
    if (files && files.length > 0) {
      await git.add(files);
    }
    await git.commit(message);
  }

  async resetHard(repoPath: string, commit: string): Promise<void> {
    const git = this.getGitInstance(repoPath);
    await git.reset(['--hard', commit]);
  }

  async resetSoft(repoPath: string, commit: string): Promise<void> {
    const git = this.getGitInstance(repoPath);
    await git.reset(['--soft', commit]);
  }

  async revert(repoPath: string, commit: string): Promise<void> {
    const git = this.getGitInstance(repoPath);
    await git.revert(commit);
  }

  async cherryPick(repoPath: string, commit: string): Promise<void> {
    const git = this.getGitInstance(repoPath);
    await git.raw(['cherry-pick', commit]);
  }

  async clone(options: CloneOptions): Promise<void> {
    const git = simpleGit();
    const cloneOptions: string[] = [];
    
    if (options.branch) {
      cloneOptions.push('--branch', options.branch);
    }
    
    if (options.depth) {
      cloneOptions.push('--depth', options.depth.toString());
    }
    
    if (options.recursive) {
      cloneOptions.push('--recursive');
    }

    await git.clone(options.url, options.directory, cloneOptions);
  }

  async getStashes(repoPath: string): Promise<Stash[]> {
    const git = this.getGitInstance(repoPath);
    const stashList = await git.stashList();
    
    return stashList.all.map((stash, index) => ({
      index,
      message: stash.message,
      hash: stash.hash,
      date: new Date(stash.date),
      author: {
        name: stash.author_name,
        email: stash.author_email
      }
    }));
  }

  async createStash(repoPath: string, message?: string): Promise<void> {
    const git = this.getGitInstance(repoPath);
    if (message) {
      await git.stash(['push', '-m', message]);
    } else {
      await git.stash();
    }
  }

  async applyStash(repoPath: string, index: number): Promise<void> {
    const git = this.getGitInstance(repoPath);
    await git.stash(['apply', `stash@{${index}}`]);
  }

  async dropStash(repoPath: string, index: number): Promise<void> {
    const git = this.getGitInstance(repoPath);
    await git.stash(['drop', `stash@{${index}}`]);
  }

  private parseCommit(commit: any): Commit {
    return {
      hash: commit.hash,
      shortHash: commit.hash.substring(0, 8),
      author: {
        name: commit.author_name,
        email: commit.author_email,
        date: new Date(commit.date)
      },
      committer: {
        name: commit.author_name, // simple-git doesn't separate committer
        email: commit.author_email,
        date: new Date(commit.date)
      },
      message: commit.message,
      shortMessage: commit.message.split('\n')[0],
      parents: commit.parents || [],
      refs: commit.refs ? commit.refs.split(', ') : [],
      body: commit.body
    };
  }

  private async getConflictedFiles(repoPath: string): Promise<string[]> {
    const git = this.getGitInstance(repoPath);
    try {
      const status = await git.status();
      return status.conflicted || [];
    } catch (error) {
      return [];
    }
  }
}
