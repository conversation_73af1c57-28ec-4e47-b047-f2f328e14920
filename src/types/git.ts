export interface Repository {
  name: string;
  path: string;
  currentBranch: string;
  isClean: boolean;
  lastCommit?: Commit;
}

export interface Commit {
  hash: string;
  shortHash: string;
  author: {
    name: string;
    email: string;
    date: Date;
  };
  committer: {
    name: string;
    email: string;
    date: Date;
  };
  message: string;
  shortMessage: string;
  parents: string[];
  refs: string[];
  body?: string;
  stats?: {
    files: number;
    insertions: number;
    deletions: number;
  };
}

export interface Branch {
  name: string;
  fullName: string;
  isRemote: boolean;
  isCurrent: boolean;
  isTracking: boolean;
  upstream?: string;
  ahead: number;
  behind: number;
  lastCommit?: Commit;
}

export interface Tag {
  name: string;
  hash: string;
  message?: string;
  tagger?: {
    name: string;
    email: string;
    date: Date;
  };
  isAnnotated: boolean;
}

export interface Remote {
  name: string;
  url: string;
  pushUrl?: string;
  type: 'fetch' | 'push' | 'both';
}

export interface FileStatus {
  path: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed' | 'copied' | 'untracked' | 'ignored';
  staged: boolean;
  oldPath?: string; // for renamed files
}

export interface GitStatus {
  current: string;
  tracking?: string;
  ahead: number;
  behind: number;
  files: FileStatus[];
  isClean: boolean;
}

export interface Stash {
  index: number;
  message: string;
  hash: string;
  date: Date;
  author: {
    name: string;
    email: string;
  };
}

export interface DiffLine {
  type: 'context' | 'added' | 'deleted' | 'header';
  content: string;
  oldLineNumber?: number;
  newLineNumber?: number;
}

export interface DiffHunk {
  header: string;
  lines: DiffLine[];
  oldStart: number;
  oldLines: number;
  newStart: number;
  newLines: number;
}

export interface FileDiff {
  path: string;
  oldPath?: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed' | 'copied';
  hunks: DiffHunk[];
  isBinary: boolean;
  stats: {
    additions: number;
    deletions: number;
  };
}

export interface CommitDiff {
  commit: Commit;
  files: FileDiff[];
  stats: {
    files: number;
    insertions: number;
    deletions: number;
  };
}

export interface GitConfig {
  user: {
    name?: string;
    email?: string;
  };
  core: {
    editor?: string;
    autocrlf?: boolean;
  };
  remote: {
    [key: string]: {
      url: string;
      fetch: string;
    };
  };
}

export interface CloneOptions {
  url: string;
  directory: string;
  branch?: string;
  depth?: number;
  recursive?: boolean;
}

export interface MergeResult {
  success: boolean;
  conflicts?: string[];
  message?: string;
}

export interface RebaseResult {
  success: boolean;
  conflicts?: string[];
  currentCommit?: string;
  totalCommits?: number;
  completedCommits?: number;
}

export interface GitLogOptions {
  branch?: string;
  limit?: number;
  skip?: number;
  since?: Date;
  until?: Date;
  author?: string;
  grep?: string;
  paths?: string[];
}
