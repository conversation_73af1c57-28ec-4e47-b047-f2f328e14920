import { app, BrowserWindow, Menu, dialog, ipcMain, shell } from 'electron';
import * as path from 'path';
import * as isDev from 'electron-is-dev';

class MainWindow {
  private window: BrowserWindow | null = null;

  constructor() {
    this.createWindow();
    this.setupEventHandlers();
    this.setupMenu();
  }

  private createWindow(): void {
    this.window = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        enableRemoteModule: true,
        webSecurity: false
      },
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      show: false
    });

    // Load the app
    if (isDev) {
      this.window.loadURL('http://localhost:3000');
      this.window.webContents.openDevTools();
    } else {
      this.window.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // Show window when ready
    this.window.once('ready-to-show', () => {
      this.window?.show();
    });

    // Handle window closed
    this.window.on('closed', () => {
      this.window = null;
    });
  }

  private setupEventHandlers(): void {
    // Handle app activation (macOS)
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });

    // Handle all windows closed
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // IPC handlers
    ipcMain.handle('select-directory', async () => {
      const result = await dialog.showOpenDialog(this.window!, {
        properties: ['openDirectory']
      });
      return result.filePaths[0];
    });

    ipcMain.handle('open-external', async (_, url: string) => {
      await shell.openExternal(url);
    });

    ipcMain.handle('show-message-box', async (_, options) => {
      const result = await dialog.showMessageBox(this.window!, options);
      return result;
    });
  }

  private setupMenu(): void {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: 'File',
        submenu: [
          {
            label: 'Open Repository',
            accelerator: 'CmdOrCtrl+O',
            click: () => {
              this.window?.webContents.send('menu-open-repository');
            }
          },
          {
            label: 'Clone Repository',
            accelerator: 'CmdOrCtrl+Shift+O',
            click: () => {
              this.window?.webContents.send('menu-clone-repository');
            }
          },
          { type: 'separator' },
          {
            label: 'Preferences',
            accelerator: 'CmdOrCtrl+,',
            click: () => {
              this.window?.webContents.send('menu-preferences');
            }
          },
          { type: 'separator' },
          {
            role: 'quit'
          }
        ]
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectall' }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Repository',
        submenu: [
          {
            label: 'Fetch',
            accelerator: 'CmdOrCtrl+Shift+F',
            click: () => {
              this.window?.webContents.send('menu-fetch');
            }
          },
          {
            label: 'Pull',
            accelerator: 'CmdOrCtrl+Shift+P',
            click: () => {
              this.window?.webContents.send('menu-pull');
            }
          },
          {
            label: 'Push',
            accelerator: 'CmdOrCtrl+P',
            click: () => {
              this.window?.webContents.send('menu-push');
            }
          }
        ]
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' }
        ]
      },
      {
        role: 'help',
        submenu: [
          {
            label: 'About SourceGit Electron',
            click: () => {
              this.window?.webContents.send('menu-about');
            }
          }
        ]
      }
    ];

    // macOS specific menu adjustments
    if (process.platform === 'darwin') {
      template.unshift({
        label: app.getName(),
        submenu: [
          { role: 'about' },
          { type: 'separator' },
          { role: 'services' },
          { type: 'separator' },
          { role: 'hide' },
          { role: 'hideOthers' },
          { role: 'unhide' },
          { type: 'separator' },
          { role: 'quit' }
        ]
      });

      // Window menu
      (template[5].submenu as Electron.MenuItemConstructorOptions[]).push(
        { type: 'separator' },
        { role: 'front' }
      );
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }
}

// App event handlers
app.whenReady().then(() => {
  new MainWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    new MainWindow();
  }
});
